import { Entity, system } from "@minecraft/server";
import { getDistance } from "../../utilities/vector3";
import {
  getAvailableAttacks,
  updateAttackHistory,
  displayAttackHistory,
  SHORT_RANGE_ATTACKS,
  MEDIUM_RANGE_ATTACKS,
  LONG_RANGE_ATTACKS
} from "./attackTracker";
import { stopPiglinChampionSounds, ATTACK_SOUND_MAP } from "./soundManager";

/**
 * Counts the number of minions belonging to a specific piglin champion within a given radius
 * @param piglinChampion The piglin champion entity
 * @param radius The search radius in blocks (default: 64)
 * @returns The number of minions belonging to this piglin champion
 */
export function countPiglinChampionMinions(piglinChampion: Entity, radius: number = 64): number {
  // Get the piglin champion's unique ID
  const championId = piglinChampion.id;

  // Search for piglin minions in the area
  const piglin_brutes = piglinChampion.dimension.getEntities({
    location: piglinChampion.location,
    maxDistance: radius,
    type: "ptd_dbb:piglin_brute"
  });

  const piglin_marauders = piglinChampion.dimension.getEntities({
    location: piglinChampion.location,
    maxDistance: radius,
    type: "ptd_dbb:piglin_marauder"
  });

  // Count minions that belong to this specific piglin champion
  let count = 0;
  for (const minion of piglin_brutes) {
    const minionChampionId = minion.getDynamicProperty("ptd_dbb:champion_id") as string;
    if (minionChampionId === championId) {
      count++;
    }
  }

  for (const minion of piglin_marauders) {
    const minionChampionId = minion.getDynamicProperty("ptd_dbb:champion_id") as string;
    if (minionChampionId === championId) {
      count++;
    }
  }

  return count;
}

/**
 * Attack range boundaries in blocks
 * Defines strict minimum and maximum distances for each attack range category
 */
export const ATTACK_RANGES = {
  close: { min: 0, max: 5 }, // 0-5 blocks: all attack types available
  medium: { min: 5, max: 7 }, // 5-7 blocks: all attack types with different probabilities
  long: { min: 7, max: 8 }, // 7-8 blocks: vertical attack, upchuck, and charging
  unreachable: { min: 12, max: 32 } // 12-32 blocks: charging attack and summoning chant
} as const;

/**
 * Selects an attack based on target distance
 * @param piglinChampion The piglin champion entity
 * @param target The target entity
 */
export async function selectAttack(piglinChampion: Entity, target: Entity): Promise<void> {
  const distance = getDistance(piglinChampion.location, target.location);

  // Close range (0-5 blocks): select from available attacks based on usage history
  if (distance >= ATTACK_RANGES.close.min && distance <= ATTACK_RANGES.close.max) {
    // Get available attacks based on usage history
    const availableAttacks = getAvailableAttacks(piglinChampion, SHORT_RANGE_ATTACKS);
    if (availableAttacks.length > 0) {
      // Randomly select one of the available attacks
      const randomIndex = Math.floor(Math.random() * availableAttacks.length);
      const attack: string = availableAttacks[randomIndex]!;

      // Stop all other sound effects except for this attack's sound
      const attackSound = ATTACK_SOUND_MAP[attack];
      stopPiglinChampionSounds(piglinChampion, attackSound);

      // Set the attack property directly instead of triggering events
      piglinChampion.setProperty("ptd_dbb:attack", attack);

      // Update attack history
      updateAttackHistory(piglinChampion, attack);
      // Display attack history on the actionbar
      displayAttackHistory(piglinChampion);
    }
  }
  // Medium range (4-8 blocks): select from available attacks based on usage history
  else if (distance > ATTACK_RANGES.medium.min && distance <= ATTACK_RANGES.medium.max) {
    // Get available attacks based on usage history
    let availableAttacks = getAvailableAttacks(piglinChampion, MEDIUM_RANGE_ATTACKS);

    // Check minion count for summoning_chant attack
    const minionCount = countPiglinChampionMinions(piglinChampion, 64);
    if (minionCount > 1) {
      // Remove summoning_chant from available attacks if there are more than 1 minion
      availableAttacks = availableAttacks.filter((attack) => attack !== "summoning_chant");
    }

    if (availableAttacks.length > 0) {
      // Randomly select one of the available attacks
      const randomIndex = Math.floor(Math.random() * availableAttacks.length);
      const attack: string = availableAttacks[randomIndex]!;

      // Stop all other sound effects except for this attack's sound
      const attackSound = ATTACK_SOUND_MAP[attack];
      stopPiglinChampionSounds(piglinChampion, attackSound);

      // Set the attack property directly instead of triggering events
      piglinChampion.setProperty("ptd_dbb:attack", attack);

      // Update attack history
      updateAttackHistory(piglinChampion, attack);
      // Display attack history on the actionbar
      displayAttackHistory(piglinChampion);
    }
  }
  // Long range (8-12 blocks): select from available attacks based on usage history
  else if (distance > ATTACK_RANGES.long.min && distance <= ATTACK_RANGES.long.max) {
    // Get available attacks based on usage history
    let availableAttacks = getAvailableAttacks(piglinChampion, LONG_RANGE_ATTACKS);

    // Check minion count for summoning_chant attack
    const minionCount = countPiglinChampionMinions(piglinChampion, 64);
    if (minionCount > 1) {
      // Remove summoning_chant from available attacks if there are more than 1 minion
      availableAttacks = availableAttacks.filter((attack) => attack !== "summoning_chant");
    }

    if (availableAttacks.length > 0) {
      // Randomly select one of the available attacks
      const randomIndex = Math.floor(Math.random() * availableAttacks.length);
      const attack: string = availableAttacks[randomIndex]!;

      // Stop all other sound effects except for this attack's sound
      const attackSound = ATTACK_SOUND_MAP[attack];
      stopPiglinChampionSounds(piglinChampion, attackSound);

      // Set the attack property directly instead of triggering events
      piglinChampion.setProperty("ptd_dbb:attack", attack);

      // Update attack history
      updateAttackHistory(piglinChampion, attack);
      // Display attack history on the actionbar
      displayAttackHistory(piglinChampion);
    }
  }
  // Unreachable range (12+ blocks): charging attack or summoning_chant
  else if (distance > ATTACK_RANGES.unreachable.min && distance <= ATTACK_RANGES.unreachable.max) {
    // 20% chance for an attack, 80% chance to not attack
    // this is evaluated per tick
    if (Math.random() < 0.2) {
      // Check minion count for summoning_chant attack
      const minionCount = countPiglinChampionMinions(piglinChampion, 64);

      // If there are 0-1 minions, allow summoning_chant (20% chance), otherwise only charging
      if (minionCount <= 1 && Math.random() < 0.2) {
        // Stop all other sound effects except for this attack's sound
        const attackSound = ATTACK_SOUND_MAP["summoning_chant"];
        stopPiglinChampionSounds(piglinChampion, attackSound);

        // Set the attack property directly instead of triggering events
        piglinChampion.setProperty("ptd_dbb:attack", "summoning_chant");
      } else {
        // Stop all other sound effects except for this attack's sound
        const attackSound = ATTACK_SOUND_MAP["charging"];
        stopPiglinChampionSounds(piglinChampion, attackSound);

        // Set the attack property directly instead of triggering events
        piglinChampion.setProperty("ptd_dbb:attack", "charging");
      }
    }
  }
  await system.waitTicks(1);
}
